<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Screenshot Container Test</title>
    <link rel="stylesheet" href="content.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        #test-results {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Screenshot Container Test Page</h1>
    
    <div class="test-section">
        <h2>1. Video Screenshot (Original Appearance)</h2>
        <p>This should appear without a container, maintaining the original styling:</p>
        <img
            src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDA5NmZmIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5WaWRlbyBTY3JlZW5zaG90PC90ZXh0Pjwvc3ZnPg=="
            alt="Video Screenshot"
            class="Stashy-video-screenshot"
            data-timestamp="00:05:30"
            data-image-data="test-data"
            data-thumbnail-data="test-thumbnail">
    </div>

    <div class="test-section">
        <h2>2. Long Screenshot (Scrollable Container)</h2>
        <p>This should appear in a scrollable container:</p>
        <div class="Stashy-screenshot-container scrollable">
            <img
                src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjgwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMCUiIHkyPSIxMDAlIj48c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZmY2YjZiO3N0b3Atb3BhY2l0eToxIiAvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6IzRlY2RjNDtzdG9wLW9wYWNpdHk6MSIgLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2dyYWQpIi8+PHRleHQgeD0iNTAlIiB5PSIxMCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkxvbmcgU2NyZWVuc2hvdDwvdGV4dD48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+U2Nyb2xsIHRvIHNlZSBtb3JlPC90ZXh0Pjx0ZXh0IHg9IjUwJSIgeT0iOTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5Cb3R0b20gb2YgaW1hZ2U8L3RleHQ+PC9zdmc+"
                alt="Long Screenshot"
                class="Stashy-video-screenshot">
        </div>
    </div>

    <div class="test-section">
        <h2>3. Test Functions</h2>
        <p>Use these buttons to test the screenshot functionality:</p>
        <button class="test-button" onclick="runConsistencyTest()">Test Screenshot Consistency</button>
        <button class="test-button" onclick="updateScreenshots()">Update Existing Screenshots</button>
        <button class="test-button" onclick="insertTestScreenshot()">Insert Test Full-Page Screenshot</button>
        
        <div id="test-results"></div>
    </div>

    <script src="content/content-screenshot.js"></script>
    <script>
        function runConsistencyTest() {
            const results = testScreenshotConsistency();
            document.getElementById('test-results').textContent = 
                'Test Results:\n' + JSON.stringify(results, null, 2);
        }

        function updateScreenshots() {
            updateExistingScreenshotsToConsistentFormat().then(() => {
                document.getElementById('test-results').textContent = 
                    'Screenshots updated successfully! Check console for details.';
            });
        }

        function insertTestScreenshot() {
            // Create a test long screenshot
            const longImageData = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjEyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkMiIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiM2NjY2ZmY7c3RvcC1vcGFjaXR5OjEiIC8+PHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZmY2NmI7c3RvcC1vcGFjaXR5OjEiIC8+PHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNjZmZjY2O3N0b3Atb3BhY2l0eToxIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZDIpIi8+PHRleHQgeD0iNTAlIiB5PSI1JSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVzdCBGdWxsLVBhZ2UgU2NyZWVuc2hvdDwvdGV4dD48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+TWlkZGxlIG9mIGltYWdlPC90ZXh0Pjx0ZXh0IHg9IjUwJSIgeT0iOTUlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5Cb3R0b20gb2YgaW1hZ2U8L3RleHQ+PC9zdmc+";
            
            insertFullPageScreenshot(longImageData, "Test Full-Page Screenshot");
            document.getElementById('test-results').textContent = 
                'Test full-page screenshot inserted! It should appear with scrolling if it\'s detected as long.';
        }

        // Auto-run consistency test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                runConsistencyTest();
            }, 1000);
        });
    </script>
</body>
</html>
